'use client';
import React, { useState, useLayoutEffect, useCallback } from 'react';
import { Box, Divider, Popover, Tooltip, Typography } from '@mui/material';
import SideMenuList from '@/components/UI/SideMenuList';
import CustomTabs from '@/components/UI/CustomTabs';
import { useRouter, useSearchParams } from 'next/navigation';
import { reportsMenuList } from '@/helper/common/commonMenus';
import CustomButton from '../UI/CustomButton';
import DownloadIcon from '@mui/icons-material/Download';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { reportsService } from '@/services/reportService';
import './reports.scss';

const Reports = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeMenuItem, setActiveMenuItem] = useState(1);
  const [activeTab, setActiveTab] = useState(1);

  const isReport = searchParams.get('is_report');
  const isActiveTab = searchParams.get('is_tab');
  const queryParams = new URLSearchParams(searchParams);

  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const [currentFilters, setCurrentFilters] = useState({});
  const exportOpen = Boolean(exportAnchorEl);
  const exportId = exportOpen ? 'export-popover' : undefined;

  // Initialize from URL parameters
  useLayoutEffect(() => {
    setActiveMenuItem(Number(isReport || 1));
  }, [isReport]);

  useLayoutEffect(() => {
    const tabIndex = Number(isActiveTab) || 1;
    setActiveTab(tabIndex);
  }, [isActiveTab]);

  // Reset filters when menu or tab changes
  useLayoutEffect(() => {
    setCurrentFilters({});
  }, [activeMenuItem, activeTab]);

  const handleActiveMenuItem = (item) => {
    setActiveMenuItem(item?.id);
    setActiveTab(1); // Reset to first tab when menu item changes
    router.push(`/reports?is_report=${item?.id}&is_tab=1`);
  };

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    queryParams.set('is_tab', tabId);
    router.push(`?${queryParams.toString()}`);
  };

  const getCurrentTabs = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );
    return currentItem?.tabs || [];
  };

  // Callback function for child components to update filters
  const handleFiltersUpdate = useCallback((filters) => {
    setCurrentFilters(filters);
  }, []);

  const getCurrentComponent = () => {
    const currentItem = reportsMenuList.find(
      (item) => item.id === activeMenuItem
    );

    // If item has tabs, check for tab-specific component
    if (currentItem?.tabs && currentItem.tabs.length > 0) {
      const currentTab = currentItem.tabs.find((tab) => tab.id === activeTab);
      const Component = currentTab?.component || currentItem?.component;

      // Clone the component and pass the filters update callback
      if (Component) {
        return React.cloneElement(Component, {
          onFiltersUpdate: handleFiltersUpdate,
          key: `${activeMenuItem}-${activeTab}`, // Add key to force re-render when tab changes
        });
      }
      return null;
    }

    // If no tabs, return main component with callback
    const Component = currentItem?.component;
    if (Component) {
      return React.cloneElement(Component, {
        onFiltersUpdate: handleFiltersUpdate,
        key: activeMenuItem, // Add key to force re-render when menu changes
      });
    }
    return null;
  };

  const handleExportClick = (event) => {
    setExportAnchorEl(event.currentTarget);
  };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  const handleExportDownload = async (format) => {
    try {
      // Use the generic export service with current filters
      const response = await reportsService.exportReport(
        activeMenuItem,
        activeTab,
        format,
        currentFilters // Pass current filters from active component
      );

      if (response.success) {
        // Generate default filename
        const reportNames = {
          1: 'rota_report',
          2: 'document_report',
          3: 'activity_report',
          4: activeTab === 1 ? 'change_request_report' : 'staff_user_report',
          5: 'log_book_report',
          6:
            activeTab === 1
              ? 'leave_balance_report'
              : 'leave_consumption_report',
          7: activeTab === 1 ? 'recipe_cta_analytics' : 'contact_submissions',
        };

        let filename = `${reportNames[activeMenuItem] || 'report'}_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : format}`;

        // Get filename from response headers if available
        const disposition = response?.headers?.['content-disposition'];
        if (disposition) {
          const match = disposition.match(/filename="?([^";]+)"?/);
          if (match) {
            filename = match[1];
          }
        }

        // Create blob and trigger download
        const blob = response.data;
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        link?.parentNode?.removeChild(link);
        window.URL.revokeObjectURL(url);
        setApiMessage('success', 'Export completed successfully');
      } else {
        setApiMessage('error', response.error || 'Export failed');
      }
    } catch (error) {
      console.error('Export error:', error);
      setApiMessage('error', error?.message || 'Export failed');
    }
    handleExportClose();
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-left">
        <Typography className="sub-header-text section-left-title">
          Reports
        </Typography>
        <Divider />

        <SideMenuList
          menuItem={reportsMenuList}
          activeId={activeMenuItem}
          onSelect={handleActiveMenuItem}
        />
      </Box>
      <Box className="section-right">
        {/* Show tabs only if current menu item has tabs */}
        {getCurrentTabs().length > 0 && (
          <Box className="section-right-tab-header">
            <Box className="report-header-tabs">
              <Box className="report-header-tabs-wrap">
                <CustomTabs
                  tabs={getCurrentTabs()}
                  initialTab={activeTab}
                  onTabChange={handleTabChange}
                />
              </Box>
              {/* Hide export button for menu items 2 and 5 */}
              {activeMenuItem !== 2 && activeMenuItem !== 5 && (
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Export Report
                        </Typography>
                      }
                      arrow
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <DownloadIcon />
                    </Tooltip>
                  }
                  onClick={handleExportClick}
                />
              )}
            </Box>
          </Box>
        )}
        <Box className="section-right-content">{getCurrentComponent()}</Box>
      </Box>

      <Popover
        className="export-popover"
        id={exportId}
        open={exportOpen}
        anchorEl={exportAnchorEl}
        onClose={handleExportClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
      >
        <Box className="export-option">
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('pdf')}
          >
            PDF
          </Typography>
          <Typography
            className="title-text pb8 cursor-pointer fw600"
            onClick={() => handleExportDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="title-text cursor-pointer fw600"
            onClick={() => handleExportDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default Reports;
