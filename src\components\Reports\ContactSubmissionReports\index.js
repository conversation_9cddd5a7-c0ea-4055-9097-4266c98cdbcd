'use client';
import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import FilterCollapse from '@/components/UI/FilterCollapse/FilterCollapse';
import ContactSubmissionReportsTable from './ContactSubmissionReportsTable';
import '../reports.scss';

export default function ContactSubmissionReports({ onFiltersUpdate }) {
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState({
    dateRange: null,
  });

  // Filter fields configuration
  const filterFields = [
    {
      name: 'search',
      type: 'search',
      label: 'Search by Name',
      searchclass: 'search-field-wrapper',
    },
    {
      name: 'dateRange',
      type: 'date-range',
      label: 'Date Range',
      placeholder: 'Select date range',
      format: 'MMM dd, yyyy',
    },
  ];

  // Handle filter apply
  const handleFilterApply = (filterValues) => {
    setFilters(filterValues);
    setSearchValue(filterValues?.search || '');

    // Prepare filters for export
    const exportFilters = {
      search: filterValues?.search || '',
      recipe_name: filterValues?.recipeName || '',
      user_email: filterValues?.userEmail || '',
      date_range: filterValues?.dateRange || '',
    };

    // Update parent component with current filters for export
    if (onFiltersUpdate) {
      onFiltersUpdate(exportFilters);
    }
  };

  // Handle field change
  const handleFieldChange = (fieldName, value) => {
    if (fieldName === 'search') {
      setSearchValue(value);
    }
  };

  // Update parent with current filters whenever filters change
  useEffect(() => {
    if (onFiltersUpdate) {
      const exportFilters = {
        search: searchValue || '',
        recipe_name: filters?.recipeName || '',
        user_email: filters?.userEmail || '',
        date_range: filters?.dateRange || '',
      };

      onFiltersUpdate(exportFilters);
    }
  }, [filters, searchValue]); // Removed onFiltersUpdate from dependencies

  return (
    <Box className="report-main-container">
      {/* Filter Section */}
      <FilterCollapse
        fields={filterFields}
        onApply={handleFilterApply}
        initialValues={filters}
        onFieldChange={handleFieldChange}
      />

      {/* Table Section */}
      <ContactSubmissionReportsTable
        searchValue={searchValue}
        filters={filters}
      />
    </Box>
  );
}
