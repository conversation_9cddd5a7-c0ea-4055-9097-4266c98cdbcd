import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import axiosInstanceMain from '@/helper/axios/axiosInstance';
import { REPORTS_URLS, URLS, RECIPE_URLS } from '@/helper/constants/urls';
import { leaveService } from '@/services/leaveService';
import {
  exportCTAAnylytics,
  exportContactSubmissions,
} from '@/services/recipeService';
import dayjs from 'dayjs';

export const reportsService = {
  // ===== ACTIVITY REPORTS =====

  // Get activity reports data
  getActivityReportsList: async (filters = {}) => {
    try {
      const {
        search = '',
        page = 1,
        size = 10,
        start_date = '',
        end_date = '',
        sort_by = '',
        sort_order = '',
      } = filters;

      // Build query parameters
      let queryParams = `?search=${search}&page=${page}&size=${size}`;
      if (start_date && end_date) {
        queryParams += `&start_date=${start_date}&end_date=${end_date}`;
      }
      if (sort_by) queryParams += `&sort_by=${sort_by}`;
      if (sort_order) queryParams += `&sort_order=${sort_order}`;

      const { status, data } = await axiosInstanceMain.get(
        URLS?.ACTIVITY_LOGS + queryParams
      );

      if (status === 200) {
        return {
          success: true,
          data: data?.data || [],
          count: data?.count || 0,
          page: data?.page || 1,
          message: 'Activity reports fetched successfully',
        };
      }
      return {
        success: false,
        data: [],
        count: 0,
        message: 'Failed to fetch activity reports',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message || 'Failed to fetch activity reports',
      };
    }
  },

  // Export activity reports
  exportActivityReport: async (format, filters = {}) => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('download', format);

      // Add pagination parameters
      if (filters.page) params.append('page', filters.page);
      if (filters.size) params.append('size', filters.size);

      // Add filter parameters if provided
      if (filters.search) params.append('search', filters.search);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);

      const response = await axiosInstanceMain.get(
        `${URLS.ACTIVITY_LOGS}?${params.toString()}`,
        { responseType: 'blob' }
      );

      return {
        success: true,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      console.error('Error exporting activity report:', error);
      return {
        success: false,
        error:
          error?.response?.data?.message || 'Failed to export activity report',
      };
    }
  },

  // ===== STAFF REPORTS =====

  // Get change request reports data
  getChangeRequestReportsList: async (filters = {}) => {
    try {
      const {
        search = '',
        page = 1,
        size = 10,
        status = '',
        start_date = '',
        end_date = '',
      } = filters;

      // Build query parameters
      let queryParams = `?search=${search}&page=${page}&size=${size}`;
      if (status) queryParams += `&status=${status}`;
      if (start_date) queryParams += `&start_date=${start_date}`;
      if (end_date) queryParams += `&end_date=${end_date}`;

      const { status: responseStatus, data } = await axiosInstanceMain.get(
        URLS?.GET_CR_LIST + queryParams
      );

      if (responseStatus === 200) {
        return {
          success: true,
          data: data?.data || [],
          count: data?.count || 0,
          page: data?.page || 1,
          message: 'Change request reports fetched successfully',
        };
      }
      return {
        success: false,
        data: [],
        count: 0,
        message: 'Failed to fetch change request reports',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch change request reports',
      };
    }
  },

  // Export change request reports
  exportChangeRequestReport: async (format, filters = {}) => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('download', format);

      // Add pagination parameters
      if (filters.page) params.append('page', filters.page);
      if (filters.size) params.append('size', filters.size);

      // Add filter parameters if provided
      if (filters.search) params.append('search', filters.search);
      if (filters.status) params.append('status', filters.status);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);

      const response = await axiosInstanceMain.get(
        `${URLS.GET_CR_LIST}?${params.toString()}`,
        { responseType: 'blob' }
      );

      return {
        success: true,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      console.error('Error exporting change request report:', error);
      return {
        success: false,
        error:
          error?.response?.data?.message ||
          'Failed to export change request report',
      };
    }
  },

  // Export staff user reports (direct implementation)
  exportStaffUserReport: async (format, filters = {}) => {
    try {
      // Format dates
      const startDatef = filters.startDate
        ? dayjs(filters.startDate)?.format('YYYY-MM-DD')
        : '';
      const endDatef = filters.endDate
        ? dayjs(filters.endDate)?.format('YYYY-MM-DD')
        : '';

      // Build query parameters
      let url =
        URLS?.EXPORT_USER_LIST +
        `?isAdmin=false&search=${filters.search || ''}&page=&size=&branch_id=${filters.branch || ''}&status=${filters.status || ''}&user_track_status=${filters.trainingStatus || ''}&contract_status=${filters.contractStatus || ''}&invitation_status=${filters.invitationStatus || ''}&role_id=${filters.role || ''}&department_id=${filters.department || ''}&download=${format}`;

      // Add filter_type if provided
      if (filters.filterType) url += `&filter_type=${filters.filterType}`;

      // Add date filters if provided
      if (startDatef) url += `&startDate=${startDatef}`;
      if (endDatef) url += `&endDate=${endDatef}`;

      // Add sorting parameters if provided
      if (filters.sortBy) url += `&sort_by=${filters.sortBy}`;
      if (filters.sortOrder) url += `&sort_order=${filters.sortOrder}`;

      const response = await axiosInstance.get(url, {
        responseType: 'blob',
      });

      if (response.status === 200) {
        return {
          success: true,
          data: response.data,
          headers: response.headers,
        };
      } else {
        return {
          success: false,
          error: 'Failed to export staff user report',
        };
      }
    } catch (error) {
      console.error('Error exporting staff user report:', error);
      return {
        success: false,
        error:
          error?.response?.data?.message ||
          error?.message ||
          'Failed to export staff user report',
      };
    }
  },

  // ===== LEAVE REPORTS =====

  // Get leave balance reports data
  getLeaveBalanceReportsList: async (filters = {}) => {
    try {
      const {
        search = '',
        page = 1,
        size = 10,
        branch_id = '',
        department_id = '',
        role_id = '',
        start_date = '',
        end_date = '',
        report_mode = 'day',
        leave_type_id = '',
        sort_by = '',
        sort_order = '',
      } = filters;

      let url = `${URLS?.GET_LEAVE_BALANCE}?branch_id=${branch_id}&department_id=${department_id}&role_id=${role_id}&search=${search}&page=${page}&size=${size}&report_mode=${report_mode}&type=report`;

      // Add leave type id if provided
      if (leave_type_id) url += `&leave_type_id=${leave_type_id}`;

      // Add date range if provided
      if (start_date) url += `&start_date=${start_date}`;
      if (end_date) url += `&end_date=${end_date}`;

      // Add sorting parameters if provided
      if (sort_by) url += `&sort_by=${sort_by}`;
      if (sort_order) url += `&sort_order=${sort_order}`;

      const response = await axiosInstanceMain.get(url);

      return {
        success: true,
        data: response.data?.data || [],
        count: response.data?.count || 0,
        message: response.data?.message || 'Data fetched successfully',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch leave balance reports',
      };
    }
  },

  // Export leave balance reports
  exportLeaveBalanceReport: async (format, filters = {}) => {
    try {
      // Build query parameters
      const params = new URLSearchParams();
      params.append('download', format);
      params.append('type', 'report'); // Always add type=report parameter

      // Add pagination parameters
      if (filters.page) params.append('page', filters.page);
      if (filters.size) params.append('size', filters.size);

      // Add all filter parameters if provided
      if (filters.branch_id) params.append('branch_id', filters.branch_id);
      if (filters.department_id)
        params.append('department_id', filters.department_id);
      if (filters.role_id) params.append('role_id', filters.role_id);
      if (filters.report_mode)
        params.append('report_mode', filters.report_mode);
      if (filters.search) params.append('search', filters.search);
      if (filters.start_date) params.append('start_date', filters.start_date);
      if (filters.end_date) params.append('end_date', filters.end_date);
      if (filters.leave_type_id)
        params.append('leave_type_id', filters.leave_type_id);
      if (filters.sort_by) params.append('sort_by', filters.sort_by);
      if (filters.sort_order) params.append('sort_order', filters.sort_order);

      const response = await axiosInstanceMain.get(
        `${URLS.GET_LEAVE_BALANCE}?${params.toString()}`,
        { responseType: 'blob' }
      );

      return {
        success: true,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      console.error('Error exporting leave balance report:', error);
      return {
        success: false,
        error:
          error?.response?.data?.message ||
          'Failed to export leave balance report',
      };
    }
  },

  // Get leave consumption reports data (using existing leaveService)
  getLeaveConsumptionReportsList: async (filters = {}) => {
    try {
      const { year = '' } = filters;

      const response = await leaveService.getLeaveUsageReport({
        year,
      });

      return response;
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message: error?.message || 'Failed to fetch leave consumption reports',
      };
    }
  },

  // Export leave consumption reports (using existing leaveService)
  exportLeaveConsumptionReport: async (format, filters = {}) => {
    try {
      const response = await leaveService.getLeaveUsageReport({
        year: filters.year || new Date().getFullYear().toString(),
        download: format === 'pdf' ? 'pdf' : format,
      });

      if (response.success) {
        return {
          success: true,
          data: response.data,
          headers: response.headers || {},
        };
      } else {
        return {
          success: false,
          error:
            response.message || 'Failed to export leave consumption report',
        };
      }
    } catch (error) {
      console.error('Error exporting leave consumption report:', error);
      return {
        success: false,
        error: error?.message || 'Failed to export leave consumption report',
      };
    }
  },

  // ===== RECIPE REPORTS =====

  // Get recipe CTA analytics data
  getRecipeCTAAnalyticsList: async (filters = {}) => {
    try {
      const {
        search = '',
        recipe_name = '',
        user_email = '',
        date_range = '',
        page = 1,
        size = 10,
      } = filters;

      // Build query parameters
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (recipe_name) params.append('recipe_name', recipe_name);
      if (user_email) params.append('user_email', user_email);
      if (date_range) params.append('date_range', date_range);
      params.append('page', page);
      params.append('size', size);

      const { status, data } = await axiosInstance.get(
        `${RECIPE_URLS.GET_PUBLIC_CTA_ANALYTICS}?${params.toString()}`
      );

      if (status === 200) {
        return {
          success: true,
          data: data?.data || [],
          count: data?.count || 0,
          page: data?.page || 1,
          message: 'Recipe CTA analytics fetched successfully',
        };
      }
      return {
        success: false,
        data: [],
        count: 0,
        message: 'Failed to fetch recipe CTA analytics',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch recipe CTA analytics',
      };
    }
  },

  // Export recipe CTA analytics (using existing recipeService)
  exportRecipeCTAAnalytics: async (format, filters = {}) => {
    try {
      const response = await exportCTAAnylytics(format, filters);

      return {
        success: true,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      console.error('Error exporting recipe CTA analytics:', error);
      return {
        success: false,
        error: error?.message || 'Failed to export recipe CTA analytics',
      };
    }
  },

  // Get contact submissions data
  getContactSubmissionsList: async (filters = {}) => {
    try {
      const {
        search = '',
        recipe_name = '',
        user_email = '',
        date_range = '',
        page = 1,
        size = 10,
      } = filters;

      // Build query parameters
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (recipe_name) params.append('recipe_name', recipe_name);
      if (user_email) params.append('user_email', user_email);
      if (date_range) params.append('date_range', date_range);
      params.append('page', page);
      params.append('size', size);

      const { status, data } = await axiosInstance.get(
        `${RECIPE_URLS.GET_PUBLIC_CONTACT_ANALYTICS}?${params.toString()}`
      );

      if (status === 200) {
        return {
          success: true,
          data: data?.data || [],
          count: data?.count || 0,
          page: data?.page || 1,
          message: 'Contact submissions fetched successfully',
        };
      }
      return {
        success: false,
        data: [],
        count: 0,
        message: 'Failed to fetch contact submissions',
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        count: 0,
        message:
          error?.response?.data?.message ||
          'Failed to fetch contact submissions',
      };
    }
  },

  // Export contact submissions (using existing recipeService)
  exportContactSubmissionsReport: async (format, filters = {}) => {
    try {
      const response = await exportContactSubmissions(format, filters);

      return {
        success: true,
        data: response.data,
        headers: response.headers,
      };
    } catch (error) {
      console.error('Error exporting contact submissions:', error);
      return {
        success: false,
        error: error?.message || 'Failed to export contact submissions',
      };
    }
  },

  // ===== GENERIC EXPORT HANDLER =====

  // Generic export handler that determines the correct export function
  exportReport: async (menuItemId, tabId, format, filters = {}) => {
    try {
      let response;

      switch (menuItemId) {
        case 3: // Activity Reports
          response = await reportsService.exportActivityReport(format, filters);
          break;
        case 4: // Staff Reports
          if (tabId === 1) {
            response = await reportsService.exportStaffUserReport(
              format,
              filters
            );
          } else if (tabId === 2) {
            response = await reportsService.exportChangeRequestReport(
              format,
              filters
            );
          }
          break;
        case 6: // Leave Reports
          if (tabId === 1) {
            response = await reportsService.exportLeaveBalanceReport(
              format,
              filters
            );
          } else if (tabId === 2) {
            response = await reportsService.exportLeaveConsumptionReport(
              format,
              filters
            );
          }
          break;
        case 7: // Recipe Reports
          if (tabId === 1) {
            response = await reportsService.exportRecipeCTAAnalytics(
              format,
              filters
            );
          } else if (tabId === 2) {
            response = await reportsService.exportContactSubmissionsReport(
              format,
              filters
            );
          }
          break;
        default:
          return {
            success: false,
            error: 'Export functionality not available for this report',
          };
      }

      return response;
    } catch (error) {
      console.error('Error in generic export handler:', error);
      return {
        success: false,
        error: error?.message || 'Export failed',
      };
    }
  },

  // ===== LEGACY ROTA REPORTS (keeping for backward compatibility) =====

  getRotaReportsList: async (filters) => {
    try {
      const { status, data } = await axiosInstance.get(
        REPORTS_URLS?.GET_ROTA_REPORTS + filters
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getEmployeeRotaDetails: async (employeeId, filters = '') => {
    try {
      const { status, data } = await axiosInstance.get(
        REPORTS_URLS?.GET_EMPLOYEE_ROTA_DETAILS + `/${employeeId}${filters}`
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getDocumentsReports: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        REPORTS_URLS.GET_SUBSCRIPTION_USAGE
      );

      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
