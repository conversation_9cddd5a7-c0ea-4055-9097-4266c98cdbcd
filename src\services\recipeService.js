import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { RECIPE_URLS } from '@/helper/constants/urls';
import { isPublicRoute } from '@/helper/common/commonFunctions';

export const getIngredientList = async (search, page, filter, Rpp, Sort) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter.status);
    params.append('type', 'ingredient');
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching ingredient list:', error);
    throw error;
  }
};

export const createIngredientCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient category');
  } catch (error) {
    throw error;
  }
};

export const updateIngredientCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient category');
  } catch (error) {
    throw error;
  }
};

export const deleteIngredientCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient category');
  } catch (error) {
    throw error;
  }
};

export const getRecipeCategoryList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter?.status);
    params.append('type', 'recipe');
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        categories: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      categories: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe categories:', error);
    throw error;
  }
};

// Public version of getRecipeCategoryList for public routes
export const getPublicRecipeCategoryList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter?.status);
    params.append('type', 'recipe');
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        categories: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      categories: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const createRecipeCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create recipe category');
  } catch (error) {
    throw error;
  }
};

export const updateRecipeCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe category');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipeCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe category');
  } catch (error) {
    throw error;
  }
};

// Public version of getAllergenList for public routes
export const getPublicAllergenList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  const allergenType =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'allergen');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (allergenType !== '') {
      params.append('isSystemAttribute', allergenType.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        allergens: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      allergens: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// Public version of getDietaryList for public routes
export const getPublicDietaryList = async (search, page, filter, Rpp, Sort) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'dietary');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        dietary: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      dietary: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getAllergenList = async (search, page, filter, Rpp, Sort) => {
  const allergenType =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'allergen');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (allergenType !== '') {
      params.append('isSystemAttribute', allergenType.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        allergens: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      allergens: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching allergen list:', error);
    throw error;
  }
};

export const createAllergen = async (allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_ALLERGEN,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create allergen');
  } catch (error) {
    throw error;
  }
};

export const updateAllergen = async (allergenId, allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_ALLERGEN}/${allergenId}`,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update allergen');
  } catch (error) {
    throw error;
  }
};

export const deleteAllergen = async (allergenId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_ALLERGEN}/${allergenId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete allergen');
  } catch (error) {
    throw error;
  }
};

// Public version of getIngredientItemsList for public routes
export const getPublicIngredientItemsList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('size', Rpp.toString());
    if (search) params.append('search', search);

    // Filter parameters - excluding status for public route
    if (filter?.category) params.append('category', filter?.category);
    if (filter?.allergen) params.append('allergen', filter?.allergen);
    if (filter?.dietary) params.append('dietary', filter?.dietary);

    // Sort parameters
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_INGREDIENTS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
        import_sample_file: data?.import_sample_file || '',
      };
    }
    return {
      ingredients: [],
      totalCount: 0,
      import_sample_file: '',
    };
  } catch (error) {
    throw error;
  }
};

export const getIngredientItemsList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('size', Rpp.toString());
    if (search) params.append('search', search);

    // Filter parameters - only append if they have values
    if (filter?.status) params.append('ingredient_status', filter?.status);
    if (filter?.category) params.append('category', filter?.category);
    if (filter?.allergen) params.append('allergy', filter?.allergen);
    if (filter?.dietary) params.append('dietary', filter?.dietary);
    if (filter?.organizationId)
      params.append('organization_id', filter?.organizationId);

    // Sort parameters - only append if both key and value exist
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_INGREDIENTS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
        import_sample_file: data?.import_sample_file || '',
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
      import_sample_file: '',
    };
  } catch (error) {
    console.error('Error fetching ingredient items list:', error);
    throw error;
  }
};

// API function to get single ingredient by ID
export const getIngredientById = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_INGREDIENT_BY_ID}/${ingredientId}`
    );

    if (status === 200) {
      return {
        ingredient: data?.data || null,
      };
    }
    return {
      ingredient: null,
    };
  } catch (error) {
    throw error;
  }
};

export const createIngredient = async (ingredientData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_INGREDIENT,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient');
  } catch (error) {
    throw error;
  }
};

export const updateIngredient = async (ingredientId, ingredientData) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_INGREDIENT}/${ingredientId}`,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to delete ingredient by ID
export const deleteIngredient = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_INGREDIENT}/${ingredientId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to export ingredient items
export const exportIngredient = async (format) => {
  try {
    // Build query string from filter

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_INGREDIENT}/${format}`,
      { responseType: 'blob' }
    );
    return response;
  } catch (error) {
    throw error;
  }
};

// API function to import ingredient categories
export const importIngredientCategory = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.IMPORT_INGREDIENT,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to import ingredient categories');
  } catch (error) {
    throw error;
  }
};

// API function to get nutrition data
export const getNutritionList = async () => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    params.append('page', '1');
    params.append('limit', '');
    params.append('search', '');
    params.append('type', 'nutrition');
    params.append('status', 'active');
    params.append('sort_by', 'attribute_title');
    params.append('sort_order', 'ASC');

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        nutrition: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      nutrition: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// API function to get dietary data
export const getDietaryList = async (search, page, filter, Rpp, Sort) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'dietary');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        dietary: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      dietary: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching dietary list:', error);
    throw error;
  }
};

// API function to get recipe measures data
export const getRecipeMeasuresList = async (
  search,
  page,
  limit,
  isStatus,
  sort_by,
  sort_order
) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (page) params.append('page', page);
    if (limit) params.append('limit', limit);
    if (isStatus) params.append('status', isStatus);
    if (sort_by) params.append('sort_by', sort_by);
    if (sort_order) params.append('sort_order', sort_order);

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.RECIPE_MEASURES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        measures: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      measures: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    // params.append('page', page || 1);
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    // params.append('limit', Rpp || 10);

    if (search) params.append('search', search);

    // Filter parameters
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Recipe status filter
      if (filter.status) {
        params.append('recipe_status', filter.status);
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }

      // Ingredient filter
      if (filter.ingredient !== undefined) {
        params.append('ingredient', filter.ingredient);
      }
      if (filter.exclude_ingredient !== undefined) {
        params.append('exclude_ingredient', filter.exclude_ingredient);
      }

      // Bookmark filter
      if (filter.bookmarked !== undefined) {
        params.append('bookmark', filter.bookmarked === 1 ? true : false);
      }

      // Ownership filter
      if (filter.ownership) {
        params.append('ownership', filter.ownership);
      }
    }

    // Sort parameters
    // if (Sort) {
    //   params.append('sort_by', Sort.key || 'updated_at');
    //   params.append('sort_order', Sort.value || 'DESC');
    // } else {
    //   params.append('sort_by', 'updated_at');
    //   params.append('sort_order', 'DESC');
    // }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_RECIPES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeSettings = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      RECIPE_URLS.RECIPES_SETTINGS
    );

    if (status === 200) {
      return data;
    }
    return {
      data: null,
      message: 'No settings found',
    };
  } catch (error) {
    console.error('Error fetching recipe settings:', error);
    throw error;
  }
};

export const updateRecipeSettings = async (settingsData) => {
  try {
    const { status, data } = await axiosInstance.put(
      RECIPE_URLS.UPDATE_RECIPES_SETTINGS,
      settingsData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe settings');
  } catch (error) {
    console.error('Error updating recipe settings:', error);
    throw error;
  }
};

export const updateBookmark = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.UPDATE_BOOKMARK}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update bookmark');
  } catch (error) {
    throw error;
  }
};

export const duplicateRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.DUPLICATE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to duplicate recipe');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe');
  } catch (error) {
    throw error;
  }
};

export const createRecipe = async (recipeData) => {
  try {
    // Configure headers for FormData
    const config = {};
    if (recipeData instanceof FormData) {
      config.headers = {
        'Content-Type': 'multipart/form-data',
      };
    }

    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_RECIPES,
      recipeData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to add recipe');
  } catch (error) {
    throw error;
  }
};

export const createUpdateRecipe = async (sectionURL, recipeData) => {
  try {
    const { status, data } = await axiosInstance.post(sectionURL, recipeData);

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to save recipe');
  } catch (error) {
    throw error;
  }
};

export const updateRecipe = async (recipeId, recipeData) => {
  try {
    // Configure headers for FormData
    const config = {};
    if (recipeData instanceof FormData) {
      config.headers = {
        'Content-Type': 'multipart/form-data',
      };
    }

    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_RECIPES}/${recipeId}`,
      recipeData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe');
  } catch (error) {
    throw error;
  }
};

export const getAttributeList = async (
  search,
  page,
  filter,
  limit,
  sort,
  type
) => {
  try {
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    if (type) params.append('type', type);

    // Add filter parameters
    if (filter?.status) params.append('status', filter.status);

    // Add sort parameters
    if (sort?.key) params.append('sort_by', sort.key);
    if (sort?.value) params.append('sort_order', sort.value);

    const url = `${RECIPE_URLS.GET_ATTRIBUTES}?${params.toString()}`;

    const { status, data } = await axiosInstance.get(url);

    if (status === 200) {
      return {
        attributes: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      attributes: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getPublicAttributeList = async (
  search,
  page,
  filter,
  limit,
  sort,
  type
) => {
  try {
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    if (type) params.append('type', type);

    // Add filter parameters
    if (filter?.status) params.append('status', filter.status);

    // Add sort parameters
    if (sort?.key) params.append('sort_by', sort.key);
    if (sort?.value) params.append('sort_order', sort.value);

    const url = `${RECIPE_URLS.GET_PUBLIC_ATTRIBUTES}?${params.toString()}`;

    const { status, data } = await axiosInstance.get(url);

    if (status === 200) {
      return {
        attributes: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      attributes: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getPublicRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    if (search) params.append('search', search);

    // Filter parameters for public recipes
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }
      if (filter.organization_slug) {
        params.append('organization_slug', filter.organization_slug);
      }
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_RECIPE_LIST}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        organization_details: data?.organization_details || '',
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      organization_details: '',
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// Get recipe preview data with conditional API call based on route type
export const getRecipePreviewData = async (slug, pathname) => {
  const isPublic = isPublicRoute(pathname);

  let apiUrl;
  if (!isPublic) {
    // For private routes, use private recipe preview API
    apiUrl = `${RECIPE_URLS.RECIPE_PREVIEW}/${slug}`;
  } else {
    // For public routes, use public recipe preview API
    apiUrl = `${RECIPE_URLS.GET_PUBLIC_RECIPE_PREVIEW}/${slug}`;
  }

  const { status, data } = await axiosInstance.get(apiUrl);

  if (status === 200 && data?.data) {
    return {
      singleRecipe: data?.data || null,
      organization_details: data?.organization_details || '',
    };
  } else {
    throw new Error('Recipe not found');
  }
};

// Track recipe view for analytics (replaces impression tracking)
export const trackRecipeView = async (trackingData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.TRACK_RECIPE_VIEW,
      trackingData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    return null;
  } catch (error) {
    console.error('Error tracking recipe view:', error);
    // Don't throw error as this is not critical functionality
    return null;
  }
};

// Track private recipe view for analytics
export const trackPrivateRecipeView = async (trackingData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.GET_PRIVATE_RECIPE_LIST,
      trackingData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    return null;
  } catch (error) {
    console.error('Error tracking private recipe view:', error);
    // Don't throw error as this is not critical functionality
    return null;
  }
};

export const getRecipeHistory = async (recipeId, page, size, viewFilter) => {
  const params = new URLSearchParams();

  if (page) params.append('page', page);
  if (size) params.append('limit', size);
  if (viewFilter) params.append('filter', viewFilter);

  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_RECIPE_HISTORY}/${recipeId}?${params.toString()}`
    );

    if (status === 200) {
      return {
        history: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      history: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe history:', error);
    throw error;
  }
};

// Get recipe view statistics for staff tracking
export const getRecipeViewStatistics = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_RECIPE_VIEW_STATISTICS}/${recipeId}`
    );

    if (status === 200) {
      return {
        data: data?.data || [],
        pagination: data?.pagination || {},
        totalRecords: data?.pagination?.total_records || 0,
      };
    }

    return {
      data: [],
      pagination: {},
      totalRecords: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe view statistics:', error);
    throw error;
  }
};

// Reset recipe view statistics
export const resetRecipeViewStatistics = async (recipeId, userIds) => {
  const params = {
    user_ids: userIds,
  };
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.RESET_RECIPE_VIEW_STATISTICS}/${recipeId}`,
      { data: params }
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to reset recipe view statistics');
  } catch (error) {
    console.error('Error resetting recipe view statistics:', error);
    throw error;
  }
};

// Get public CTA analytics data
export const getPublicCTAAnalytics = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  try {
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);

    // Filter parameters - only append if they have values
    if (filter?.date_range) params.append('date_range', filter?.date_range);
    if (filter?.cta_type) params.append('cta_type', filter?.cta_type);
    if (filter?.ctaType) params.append('cta_type', filter?.ctaType); // Support both parameter names for backward compatibility
    if (filter?.recipe_name) params.append('search', filter?.recipe_name);
    if (filter?.recipe) params.append('recipe_name', filter?.recipe); // Support both parameter names for backward compatibility

    // Sort parameters - only append if both key and value exist
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_CTA_ANALYTICS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        data: data?.data || [],
        meta: data?.meta || {},
        pagination: data?.pagination || {},
        totalRecords: data?.pagination?.total_records || 0,
      };
    }

    return {
      data: [],
      meta: {},
      pagination: {},
      totalRecords: 0,
    };
  } catch (error) {
    console.error('Error fetching CTA analytics:', error);
    throw error;
  }
};

// Get public contact analytics data
export const getPublicContactAnalytics = async (page, filter, Rpp, Sort) => {
  try {
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());

    // Filter parameters - only append if they have values
    if (filter?.date_range) params.append('date_range', filter?.date_range);
    if (filter?.recipe) params.append('recipe_name', filter?.recipe);
    if (filter?.recipe_name) params.append('search', filter?.recipe_name);

    // Sort parameters - only append if both key and value exist
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_CONTACT_ANALYTICS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        data: data?.data || [],
        meta: data?.meta || {},
        pagination: data?.pagination || {},
        totalRecords: data?.pagination?.total_records || 0,
      };
    }

    return {
      data: [],
      meta: {},
      pagination: {},
      totalRecords: 0,
    };
  } catch (error) {
    console.error('Error fetching contact analytics:', error);
    throw error;
  }
};

// Get public analytics overview data for admin dashboard
export const getPublicAnalyticsOverview = async (filters = {}) => {
  try {
    const params = new URLSearchParams();

    // Add filter parameters if provided
    if (filters.date_range) params.append('date_range', filters.date_range);
    if (filters.category) params.append('category', filters.category);
    if (filters.userType) params.append('userType', filters.userType);
    if (filters.region) params.append('region', filters.region);
    if (filters.device) params.append('device', filters.device);

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_ANALYTICS_OVERVIEW}?${params.toString()}`
    );

    if (status === 200) {
      return data?.data || {};
    }

    return {};
  } catch (error) {
    console.error('Error fetching public analytics overview:', error);
    throw error;
  }
};

// Recipe Assignment API functions
export const assignRecipeToUsers = async (recipeId, userIds) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.ASSIGN_RECIPE,
      {
        recipe_id: recipeId,
        user_ids: userIds,
      }
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to assign recipe to users');
  } catch (error) {
    throw error;
  }
};

export const exportRecipe = async (recipeId, format) => {
  try {
    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_RECIPE}/${recipeId}?format=${format}`,
      { responseType: 'blob' }
    );

    return response;
  } catch (error) {
    throw error;
  }
};

// API function to export recipe dashboard analytics
export const exportRecipeDashboard = async (format, filters = {}) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Add format parameter
    params.append('format', format);

    // Add filter parameters if provided
    if (filters.date_range) params.append('date_range', filters.date_range);
    if (filters.category) params.append('category', filters.category);
    if (filters.userType) params.append('userType', filters.userType);
    if (filters.region) params.append('region', filters.region);
    if (filters.device) params.append('device', filters.device);

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_RECIPE_DASHBOARD}?${params.toString()}`,
      { responseType: 'blob' }
    );

    return response;
  } catch (error) {
    throw error;
  }
};

// Track CTA clicks for analytics
export const trackCtaClick = async (trackingData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.TRACK_CTA_CLICKS,
      trackingData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    return null;
  } catch (error) {
    console.error('Error tracking CTA click:', error);
    // Don't throw error as this is not critical functionality
    return null;
  }
};

// Submit contact form for public recipes
export const submitContactForm = async (contactData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.SUBMIT_CONTACT_FORM,
      contactData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to submit contact form');
  } catch (error) {
    throw error;
  }
};

// Delete public contact analytics submission
export const deleteContactSubmission = async (submissionId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_PUBLIC_CONTACT_ANALYTICS}/${submissionId}`
    );

    if (status === 200 || status === 204) {
      return data;
    }
    throw new Error('Failed to delete contact submission');
  } catch (error) {
    throw error;
  }
};

// Export contact submissions analytics
export const exportContactSubmissions = async (format, filters = {}) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Add download parameter instead of format
    params.append('download', format);

    // Add pagination parameters
    if (filters.page) params.append('page', filters.page);
    if (filters.size) params.append('size', filters.size);

    // Add filter parameters if provided
    if (filters.search) params.append('search', filters.search);
    if (filters.recipe_name) params.append('recipe_name', filters.recipe_name);
    if (filters.user_email) params.append('user_email', filters.user_email);
    if (filters.date_range) params.append('date_range', filters.date_range);

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_PUBLIC_CONTACT_ANALYTICS}?${params.toString()}`,
      { responseType: 'blob' }
    );

    return response;
  } catch (error) {
    throw error;
  }
};

export const exportCTAAnylytics = async (format, filters = {}) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Add download parameter instead of format
    params.append('download', format);

    // Add type=report parameter for Reports section
    params.append('type', 'report');

    // Add pagination parameters
    if (filters.page) params.append('page', filters.page);
    if (filters.size) params.append('size', filters.size);

    // Add filter parameters if provided
    if (filters.search) params.append('search', filters.search);
    if (filters.recipe_name) params.append('recipe_name', filters.recipe_name);
    if (filters.user_email) params.append('user_email', filters.user_email);
    if (filters.date_range) params.append('date_range', filters.date_range);
    if (filters.cta_type) params.append('cta_type', filters.cta_type);

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_CTA_ANALYTICS}?${params.toString()}`,
      { responseType: 'blob' }
    );

    return response;
  } catch (error) {
    throw error;
  }
};

// Upload a recipe file (image, video, doc, audio, etc.)
export const uploadRecipeFile = async (formData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.UPLOAD_RECIPE_FILES,
      formData,
      {
        headers: { 'Content-Type': 'multipart/form-data' },
      }
    );
    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to upload recipe file');
  } catch (error) {
    throw error;
  }
};

// Delete an uploaded file by key/reference
export const deleteUploadedFile = async (recipeResources, recipeId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      RECIPE_URLS.DELETE_RECIPE_FILES,
      {
        data: { recipe_resources: recipeResources, recipe_id: recipeId },
      }
    );
    if (status === 200 || status === 204) {
      return data;
    }
    throw new Error('Failed to delete uploaded file');
  } catch (error) {
    throw error;
  }
};

export const checkOrganizationSlug = async (slug) => {
  try {
    const params = new URLSearchParams();
    params.append('slug', slug);
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.ORGANIZATION_SLUGS}?${params.toString()}`
    );
    if (status === 200) {
      return data;
    }
    throw new Error('Failed to check organization slug');
  } catch (error) {
    throw error;
  }
};
